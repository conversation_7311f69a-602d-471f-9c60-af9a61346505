import { defineRouteConfig } from "@camped-ai/admin-sdk";
import { PlusMini } from "@camped-ai/icons";
import { Upload as UploadIcon, Download, MapPin } from "lucide-react";
import { ComponentType } from "react";
import OutlineButton from "../../../components/shared/OutlineButton";
import {
  StatusFilterBadges,
  StatusBadges,
} from "../../../components/shared/StatusFilterBadges";

// Create icon component for route config
const DestinationIcon: ComponentType = (props: any) => (
  <MapPin {...props} className="h-4 w-4" />
);

import {
  Container,
  Heading,
  Text,
  Button,
  Input,
  Toaster,
  toast,
  FocusModal,
  Select,
} from "@camped-ai/ui";
import { useState } from "react";
import { useNavigate } from "react-router-dom";

import DestinationFormModern, {
  DestinationFormData,
} from "../../../components/destination-form-modern";
import BulkImportModal from "../../../components/destination/bulk-import-modal";
import ExportModal from "../../../components/destination/export-modal";
import PermissionBasedSidebarHider from "../../../widgets/permission-based-sidebar-hider";
import { useRbac } from "../../../hooks/use-rbac";
import { useDestinationsManagement } from "../../../hooks/hotel-management/use-destinations-management";
import DestinationSkeleton from "../../../components/destination/destination-skeleton";
import "../../../styles/destination-modal-fix.css";
import "../../../styles/destination-badges.css";

const DestinationsPage = () => {
  const navigate = useNavigate();
  const { hasPermission } = useRbac();

  // Use the new destinations management hook
  const {
    destinations,
    isLoading,
    isFetching,
    filters,
    pagination,
    nextPage,
    previousPage,
    goToPage,
    changePageSize,
    updateSearch,
    updateStatusFilter,
    clearFilters,
    refetch,
  } = useDestinationsManagement();

  const [open, setOpen] = useState(false);

  // Define the initial form data as a constant to ensure consistency
  const initialFormData: DestinationFormData = {
    name: "",
    handle: "",
    description: "",
    is_active: true,
    is_featured: false,
    country: "",
    location: null,
    tags: null,
    website: null,
    media: [],
    faqs: [],
  };
  const [bulkImportOpen, setBulkImportOpen] = useState(false);
  const [exportModalOpen, setExportModalOpen] = useState(false);
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [showFilters, setShowFilters] = useState(false);
  const [formData, setFormData] =
    useState<DestinationFormData>(initialFormData);

  // Function to handle modal close with proper form reset
  const handleModalClose = (isOpen: boolean) => {
    setOpen(isOpen);

    if (!isOpen) {
      // Modal is closing - reset form data to initial state
      console.log("🔄 Modal closing - resetting form data to initial state", {
        previousMediaCount: formData.media?.length || 0,
        previousFaqCount: formData.faqs?.length || 0,
      });
      setFormData({ ...initialFormData });
    } else {
      // Modal is opening
      console.log("🔄 Modal opening - current form state:", {
        mediaCount: formData.media?.length || 0,
        faqCount: formData.faqs?.length || 0,
        hasName: !!formData.name,
      });
    }
  };

  // Since filtering is now handled by the backend API, we don't need client-side filtering
  // The destinations from the hook are already filtered and paginated

  // Get a gradient color based on index
  const getGradient = (index: number) => {
    const gradients = [
      "from-blue-500 to-purple-500",
      "from-green-500 to-teal-500",
      "from-yellow-500 to-orange-500",
      "from-pink-500 to-rose-500",
      "from-indigo-500 to-blue-500",
      "from-red-500 to-pink-500",
    ];
    return gradients[index % gradients.length];
  };

  const handleCreate = async (updatedData?: DestinationFormData) => {
    // Use the updated data if provided, otherwise use the current formData
    const dataToUse = updatedData || formData;

    // Debug: Log the received data structure
    console.log("🔍 handleCreate received data:", {
      hasUpdatedData: !!updatedData,
      mediaCount: dataToUse.media?.length || 0,
      mediaDetails:
        dataToUse.media?.map((m) => ({
          hasFile: !!m?.file,
          fileName: m?.file?.name,
          fileSize: m?.file?.size,
          fileType: m?.file?.type,
          isThumbnail: m?.isThumbnail,
        })) || [],
      faqCount: dataToUse.faqs?.length || 0,
    });

    try {
      // First create the destination
      // Ensure tags is properly formatted
      let formattedTags = dataToUse.tags;
      if (typeof dataToUse.tags === "string") {
        try {
          // Try to parse if it's a JSON string
          formattedTags = JSON.parse(dataToUse.tags as string);
        } catch (e) {
          // If not a valid JSON, split by comma
          formattedTags = (dataToUse.tags as string)
            .split(",")
            .map((tag) => tag.trim());
        }
      }

      const destinationData = {
        name: dataToUse.name,
        handle: dataToUse.handle,
        description: dataToUse.description,
        is_active: dataToUse.is_active,
        is_featured: dataToUse.is_featured,
        country: dataToUse.country,
        location: dataToUse.location,
        tags: formattedTags,
        website: dataToUse.website,
        faqs: dataToUse.faqs || [], // Include FAQs in the API payload
      };

      // Validate FAQ data before submission
      if (destinationData.faqs && destinationData.faqs.length > 0) {
        const invalidFaqs = destinationData.faqs.filter(
          (faq) => !faq.question?.trim() || !faq.answer?.trim()
        );

        if (invalidFaqs.length > 0) {
          toast.error("Invalid FAQ Data", {
            description:
              "All FAQ entries must have both question and answer filled out.",
          });
          return false;
        }
      }

      // Debug: Log FAQ data being sent to API
      console.log("🔍 Destination creation payload:", {
        name: destinationData.name,
        faqCount: destinationData.faqs?.length || 0,
        faqs: destinationData.faqs,
        hasMedia: (dataToUse.media?.length || 0) > 0,
        mediaCount: dataToUse.media?.length || 0,
      });

      const response = await fetch("/admin/hotel-management/destinations", {
        method: "POST",
        credentials: "include",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(destinationData),
      });

      const data = await response.json();

      if (response.ok && data.destination) {
        const destinationId = data.destination.id;

        // Log successful destination creation with FAQ info
        console.log("✅ Destination created successfully:", {
          id: destinationId,
          name: data.destination.name,
          faqsCreated: data.destination.faqs?.length || 0,
        });

        // If there are images to upload
        if (dataToUse.media && dataToUse.media.length > 0) {
          console.log(`📸 Uploading ${dataToUse.media.length} images...`);

          // Validate images before upload
          const validImages = dataToUse.media.filter((media) => {
            console.log("🔍 Validating media item:", {
              hasFile: !!media.file,
              fileName: media.file?.name,
              fileSize: media.file?.size,
              fileType: media.file?.type,
              url: media.url?.substring(0, 50) + "...",
            });

            if (!media.file) {
              console.log("❌ Media item rejected: No file object");
              return false;
            }

            // Check file size (max 10MB)
            const maxSize = 10 * 1024 * 1024; // 10MB
            if (media.file.size > maxSize) {
              toast.error("Image Too Large", {
                description: `"${media.file.name}" is too large. Maximum size is 10MB.`,
              });
              return false;
            }

            // Check file type
            const allowedTypes = [
              "image/jpeg",
              "image/jpg",
              "image/png",
              "image/webp",
            ];
            if (!allowedTypes.includes(media.file.type)) {
              toast.error("Invalid Image Type", {
                description: `"${media.file.name}" is not a supported image format. Use JPEG, PNG, or WebP.`,
              });
              return false;
            }

            console.log("✅ Media item passed validation");
            return true;
          });

          console.log(
            `🔍 Validation results: ${validImages.length}/${dataToUse.media.length} images passed validation`
          );

          if (validImages.length !== dataToUse.media.length) {
            console.log(
              `⚠️ ${
                dataToUse.media.length - validImages.length
              } images failed validation`
            );
          }

          let uploadedCount = 0;
          let failedCount = 0;

          // Upload each validated image
          for (const media of validImages) {
            if (media.file) {
              console.log(`🔍 Preparing upload for: ${media.file.name}`);

              const formData = new FormData();
              formData.append("files", media.file);

              // Add metadata including thumbnail flag
              const metadata = {
                isThumbnail: media.isThumbnail,
              };
              formData.append("metadata", JSON.stringify(metadata));

              console.log(`🔍 FormData prepared for ${media.file.name}:`, {
                fileSize: media.file.size,
                fileType: media.file.type,
                metadata: metadata,
                destinationId: destinationId,
              });

              try {
                const uploadResponse = await fetch(
                  `/admin/hotel-management/destinations/${destinationId}/upload`,
                  {
                    method: "POST",
                    credentials: "include",
                    body: formData,
                  }
                );

                console.log(`🔍 Upload response for ${media.file.name}:`, {
                  status: uploadResponse.status,
                  statusText: uploadResponse.statusText,
                  ok: uploadResponse.ok,
                });

                if (!uploadResponse.ok) {
                  const errorText = await uploadResponse.text();
                  console.error(
                    `❌ Failed to upload image "${media.file.name}":`,
                    {
                      status: uploadResponse.status,
                      statusText: uploadResponse.statusText,
                      errorText: errorText,
                    }
                  );
                  failedCount++;

                  // Show user-friendly error for image upload failure
                  toast.error("Image Upload Failed", {
                    description: `Failed to upload "${media.file.name}". Please try again.`,
                  });
                } else {
                  const responseData = await uploadResponse.json();
                  uploadedCount++;
                  console.log(
                    `✅ Successfully uploaded image "${media.file.name}":`,
                    responseData
                  );
                }
              } catch (uploadError) {
                console.error(
                  `❌ Error uploading image "${media.file.name}":`,
                  uploadError
                );
                failedCount++;

                // Show user-friendly error for network/other errors
                toast.error("Upload Error", {
                  description: `Network error while uploading "${media.file.name}". Please check your connection.`,
                });
              }
            }
          }

          // Summary logging
          console.log(
            `📸 Image upload summary: ${uploadedCount} successful, ${failedCount} failed`
          );

          // Show summary toast if there were mixed results
          if (uploadedCount > 0 && failedCount > 0) {
            toast.warning("Partial Upload Success", {
              description: `${uploadedCount} images uploaded successfully, ${failedCount} failed.`,
            });
          } else if (uploadedCount > 0 && failedCount === 0) {
            console.log("✅ All images uploaded successfully");
            toast.success("Images Uploaded", {
              description: `Successfully uploaded ${uploadedCount} image${
                uploadedCount > 1 ? "s" : ""
              }.`,
            });
          } else if (validImages.length > 0 && uploadedCount === 0) {
            toast.error("Upload Failed", {
              description: "All image uploads failed. Please try again.",
            });
          }
        }

        // Handle translations if we have translation data
        console.log("Checking for translation data:", {
          hasTranslationData: !!dataToUse.translationData,
          destinationId,
        });
        if (dataToUse.translationData) {
          console.log(
            "Saving translations for new destination:",
            destinationId,
            dataToUse.translationData
          );
          try {
            await dataToUse.translationData.saveFunction(destinationId);
            console.log("Translations saved successfully for new destination");
          } catch (translationError) {
            console.error(
              "Error saving translations for new destination:",
              translationError
            );
            // Don't fail the entire creation process if translations fail
          }
        } else {
          console.log("No translation data to save for new destination");
        }

        toast.success("Success", {
          description: "Destination created successfully",
        });

        // Reset form data to initial state
        setFormData({ ...initialFormData });

        refetch();
        setOpen(false);
        return true;
      } else {
        // Enhanced error handling with specific feedback
        console.error("❌ Destination creation failed:", {
          status: response.status,
          statusText: response.statusText,
          data: data,
          faqCount: destinationData.faqs?.length || 0,
          hasImages: (dataToUse.media?.length || 0) > 0,
        });

        // Provide specific error messages based on the response
        let errorMessage = "Failed to create destination";
        if (data.message) {
          errorMessage = data.message;
        } else if (response.status === 400) {
          errorMessage =
            "Invalid data provided. Please check all fields and try again.";
        } else if (response.status === 403) {
          errorMessage = "You don't have permission to create destinations.";
        } else if (response.status === 409) {
          errorMessage = "A destination with this handle already exists.";
        } else if (response.status >= 500) {
          errorMessage = "Server error occurred. Please try again later.";
        }

        // Check for FAQ-specific errors
        if (data.errors && Array.isArray(data.errors)) {
          const faqErrors = data.errors.filter(
            (err: any) => err.path && err.path.includes("faqs")
          );
          if (faqErrors.length > 0) {
            errorMessage += " Please check your FAQ entries.";
          }
        }

        toast.error("Creation Failed", {
          description: errorMessage,
        });
        return false; // Return false on failure
      }
    } catch (error) {
      console.error("❌ Unexpected error creating destination:", error);

      // Provide more helpful error messages based on error type
      let errorMessage = "An unexpected error occurred";
      if (error instanceof TypeError && error.message.includes("fetch")) {
        errorMessage =
          "Network error. Please check your connection and try again.";
      } else if (error instanceof SyntaxError) {
        errorMessage = "Invalid response from server. Please try again.";
      }

      toast.error("Error", {
        description: errorMessage,
      });
      return false;
    }
  };

  return (
    <>
      <PermissionBasedSidebarHider />
      <Toaster />

      {/* Create Destination Drawer */}
      <FocusModal open={open} onOpenChange={handleModalClose}>
        <FocusModal.Content className="w-full h-full destination-modal">
          <DestinationFormModern
            key={open ? "destination-form-open" : "destination-form-closed"}
            formData={formData}
            setFormData={setFormData}
            onSubmit={handleCreate}
            closeModal={() => handleModalClose(false)}
          />
        </FocusModal.Content>
      </FocusModal>

      {/* Bulk Import Modal */}
      <BulkImportModal
        open={bulkImportOpen}
        onClose={() => {
          setBulkImportOpen(false);
          // Refresh data when modal closes with a slight delay to ensure server has processed everything
          setTimeout(() => {
            console.log("Refreshing destinations after import");
            refetch();
          }, 500);
        }}
      />

      {/* Export Modal */}
      <ExportModal
        open={exportModalOpen}
        onClose={() => setExportModalOpen(false)}
      />

      <Container className="space-y-6">
        <div className="flex justify-between items-center">
          <div>
            <Heading level="h1" className="text-2xl">
              Destinations
            </Heading>
            <Text className="text-muted-foreground">
              Manage your travel destinations
            </Text>
          </div>

          <div className="flex gap-2">
            <OutlineButton
              size="small"
              onClick={() => setExportModalOpen(true)}
              className="flex items-center gap-2 px-4 py-2 rounded-md transition-all"
            >
              <Download className="w-4 h-4" />
              <span>Export</span>
            </OutlineButton>
            {hasPermission("destinations:bulk_import") && (
              <OutlineButton
                size="small"
                onClick={() => setBulkImportOpen(true)}
                className="flex items-center gap-2 px-4 py-2 rounded-md transition-all"
              >
                <UploadIcon className="w-4 h-4" />
                <span>Import</span>
              </OutlineButton>
            )}

            {hasPermission("destinations:create") && (
              <Button
                variant="primary"
                size="small"
                onClick={() => setOpen(true)}
                className="shadow-sm flex items-center gap-2 px-4 py-2 rounded-md transition-all"
              >
                <PlusMini className="w-4 h-4" />
                <span>Add Destination</span>
              </Button>
            )}
          </div>
        </div>

        <div className="flex flex-col md:flex-row gap-4">
          <div className="relative flex-grow">
            <Input
              placeholder="Search destinations..."
              value={filters.search || ""}
              onChange={(e) => updateSearch(e.target.value)}
              className="h-9 bg-background border-border text-foreground placeholder:text-muted-foreground focus:ring-ring focus:border-ring pr-10"
            />
            {isFetching && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <div className="animate-spin h-4 w-4 border-2 border-ui-border-base border-t-ui-fg-base rounded-full"></div>
              </div>
            )}
          </div>
          <Button
            variant="secondary"
            size="small"
            onClick={() => setShowFilters(!showFilters)}
            className="whitespace-nowrap bg-background border border-border shadow-sm hover:bg-muted flex items-center gap-2 px-3 py-2 rounded-md transition-all"
          >
            <svg
              className="w-4 h-4"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z"
              />
            </svg>
            <span>{showFilters ? "Hide Filters" : "Show Filters"}</span>
          </Button>

          <div className="flex gap-2">
            <div className="flex gap-2">
              <Button
                variant={viewMode === "grid" ? "primary" : "secondary"}
                size="small"
                onClick={() => setViewMode("grid")}
                // className={`px-3 py-2 rounded-md shadow-sm flex items-center justify-center ${
                //   viewMode === "grid"
                //     ? "bg-primary text-primary-foreground"
                //     : "bg-background border border-border hover:bg-accent text-foreground"
                // }`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-5 h-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z" />
                </svg>
              </Button>

              <Button
                variant={viewMode === "list" ? "primary" : "secondary"}
                size="small"
                onClick={() => setViewMode("list")}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="w-5 h-5"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M3 5a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 10a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zM3 15a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
              </Button>
            </div>
          </div>
        </div>

        {/* Collapsible Filter Section */}
        {showFilters && (
          <StatusFilterBadges
            filters={filters}
            updateStatusFilter={updateStatusFilter}
          />
        )}

        {isLoading || isFetching ? (
          <DestinationSkeleton
            viewMode={viewMode}
            count={viewMode === "grid" ? 6 : 10}
          />
        ) : destinations.length > 0 ? (
          viewMode === "grid" ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {destinations.map((destination, index) => (
                <div
                  key={destination.id}
                  className="border border-border rounded-lg overflow-hidden bg-card shadow-sm hover:shadow-md transition-all cursor-pointer flex flex-col"
                  onClick={() =>
                    navigate(
                      `/hotel-management/destinations/${destination.handle}`
                    )
                  }
                >
                  <div className="h-40 relative flex-shrink-0">
                    {destination.thumbnailUrl ? (
                      <>
                        <img
                          src={destination.thumbnailUrl}
                          alt={destination.name}
                          className="w-full h-full object-cover"
                        />
                        <div className="absolute inset-0 bg-black bg-opacity-40 flex items-center justify-center">
                          <Heading
                            level="h3"
                            className="text-white text-2xl font-bold"
                          >
                            {destination.name}
                          </Heading>
                        </div>
                      </>
                    ) : (
                      <div
                        className={`h-full w-full bg-gradient-to-r ${getGradient(
                          index
                        )} flex items-center justify-center`}
                      >
                        <Heading
                          level="h3"
                          className="text-white text-2xl font-bold"
                        >
                          {destination.name}
                        </Heading>
                      </div>
                    )}
                    {/* Hotel count badge */}
                    <div className="absolute top-2 right-2 bg-white/45 rounded-full px-2 py-1 text-xs font-medium flex items-center gap-1 shadow-sm">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="w-3.5 h-3.5 text-blue-600"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                      </svg>
                      <span className="text-foreground">
                        {destination.hotelCount || 0} Hotels
                      </span>
                    </div>
                  </div>
                  <div className="p-4 flex flex-col flex-grow">
                    <div className="flex-grow flex flex-col">
                      <div className="flex justify-between items-center mb-2">
                        <Text className="font-medium">
                          {destination.country}
                        </Text>
                        <StatusBadges
                          isActive={destination.is_active}
                          isFeatured={destination.is_featured}
                          size="small"
                        />
                      </div>
                      <Text className="text-sm text-muted-foreground line-clamp-2 mb-3">
                        {destination.description || "No description available"}
                      </Text>
                    </div>

                    {/* Action buttons */}
                    <div className="flex gap-2 mt-auto">
                      <Button
                        variant="secondary"
                        size="small"
                        className="bg-background border border-border shadow-sm hover:bg-accent flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-foreground text-xs flex-1"
                        onClick={(e) => {
                          e.stopPropagation();
                          navigate(
                            `/hotel-management/destinations/${destination.handle}`
                          );
                        }}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="w-3.5 h-3.5"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                          <path
                            fillRule="evenodd"
                            d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                            clipRule="evenodd"
                          />
                        </svg>
                        <span>View</span>
                      </Button>
                      <Button
                        variant="secondary"
                        size="small"
                        className="bg-background border border-border shadow-sm hover:bg-accent flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-foreground text-xs flex-1"
                        onClick={(e) => {
                          e.stopPropagation();
                          // Navigate to hotels filtered by this destination
                          navigate(
                            `/hotel-management/hotels?destination=${destination.id}`
                          );
                        }}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="w-3.5 h-3.5"
                          viewBox="0 0 20 20"
                          fill="currentColor"
                        >
                          <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                        </svg>
                        <span>Hotels</span>
                      </Button>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <div className="border border-border rounded-lg overflow-hidden bg-card shadow-sm">
              <table className="min-w-full divide-y divide-border">
                <thead className="bg-muted/50">
                  <tr>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"
                    >
                      Destination
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"
                    >
                      Country
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"
                    >
                      Hotels
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"
                    >
                      Status
                    </th>
                    <th
                      scope="col"
                      className="px-6 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider"
                    >
                      Actions
                    </th>
                  </tr>
                </thead>
                <tbody className="bg-card divide-y divide-border">
                  {destinations.map((destination) => (
                    <tr
                      key={destination.id}
                      className="hover:bg-accent cursor-pointer"
                      onClick={() =>
                        navigate(
                          `/hotel-management/destinations/${destination.handle}`
                        )
                      }
                    >
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10 relative">
                            {destination.thumbnailUrl ? (
                              <img
                                className="h-10 w-10 rounded-md object-cover"
                                src={destination.thumbnailUrl}
                                alt=""
                              />
                            ) : (
                              <div className="h-10 w-10 rounded-md bg-gradient-to-r from-blue-500 to-purple-500"></div>
                            )}
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-foreground">
                              {destination.name}
                            </div>
                            <div className="text-sm text-muted-foreground truncate max-w-xs">
                              {destination.description || "No description"}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-foreground">
                          {destination.country || "--"}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-foreground font-medium">
                          {destination.hotelCount || 0}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <StatusBadges
                          isActive={destination.is_active}
                          isFeatured={destination.is_featured}
                          size="small"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <div className="flex gap-2">
                          <Button
                            variant="secondary"
                            size="small"
                            className="bg-background border border-border shadow-sm hover:bg-accent flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-foreground text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              navigate(
                                `/hotel-management/destinations/${destination.handle}`
                              );
                            }}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="w-3.5 h-3.5"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                              <path
                                fillRule="evenodd"
                                d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                            <span>View</span>
                          </Button>
                          <Button
                            variant="secondary"
                            size="small"
                            className="bg-background border border-border shadow-sm hover:bg-accent flex items-center gap-1 px-3 py-1.5 rounded-md transition-all text-foreground text-xs"
                            onClick={(e) => {
                              e.stopPropagation();
                              // Navigate to hotels filtered by this destination
                              navigate(
                                `/hotel-management/hotels?destination=${destination.id}`
                              );
                            }}
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              className="w-3.5 h-3.5"
                              viewBox="0 0 20 20"
                              fill="currentColor"
                            >
                              <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
                            </svg>
                            <span>Hotels</span>
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )
        ) : (
          <div className="text-center py-12 bg-muted/50 rounded-lg">
            <Text className="text-muted-foreground">
              {filters.search ||
              filters.is_featured !== null ||
              filters.is_active !== null
                ? "No destinations match your search criteria"
                : "No destinations found"}
            </Text>
          </div>
        )}

        {/* Pagination controls */}
        {destinations.length > 0 && (
          <div className="flex items-center justify-between p-4 border-t border-ui-border-base bg-ui-bg-base">
            {/* Total count on the left */}
            <div className="flex items-center">
              <Text className="text-sm text-ui-fg-base font-medium">
                Total Arrivals: {pagination.totalCount}
              </Text>
            </div>

            {/* Page numbers in the center */}
            <div className="flex items-center gap-1">
              {/* Previous arrow */}
              <Button
                variant="secondary"
                size="small"
                disabled={!pagination.canPreviousPage}
                onClick={previousPage}
                className={`w-8 h-8 p-0 flex items-center justify-center border border-ui-border-base ${
                  !pagination.canPreviousPage
                    ? "bg-ui-bg-disabled text-ui-fg-disabled cursor-not-allowed"
                    : "bg-ui-bg-base text-ui-fg-base hover:bg-ui-bg-subtle"
                }`}
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M10 12L6 8L10 4"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </Button>

              {/* Page numbers */}
              {(() => {
                const pages = [];
                const maxVisiblePages = 5;
                let startPage = Math.max(
                  1,
                  pagination.currentPage - Math.floor(maxVisiblePages / 2)
                );
                let endPage = Math.min(
                  pagination.totalPages,
                  startPage + maxVisiblePages - 1
                );

                // Adjust start page if we're near the end
                if (endPage - startPage + 1 < maxVisiblePages) {
                  startPage = Math.max(1, endPage - maxVisiblePages + 1);
                }

                for (let i = startPage; i <= endPage; i++) {
                  pages.push(
                    <Button
                      key={i}
                      variant={
                        i === pagination.currentPage ? "primary" : "secondary"
                      }
                      size="small"
                      onClick={() => goToPage(i)}
                      className={`w-8 h-8 p-0 flex items-center justify-center border ${
                        i === pagination.currentPage
                          ? "bg-ui-bg-interactive text-ui-fg-on-color border-ui-bg-interactive"
                          : "bg-ui-bg-base text-ui-fg-base border-ui-border-base hover:bg-ui-bg-subtle"
                      }`}
                    >
                      {i}
                    </Button>
                  );
                }
                return pages;
              })()}

              {/* Next arrow */}
              <Button
                variant="secondary"
                size="small"
                disabled={!pagination.canNextPage}
                onClick={nextPage}
                className={`w-8 h-8 p-0 flex items-center justify-center border border-ui-border-base ${
                  !pagination.canNextPage
                    ? "bg-ui-bg-disabled text-ui-fg-disabled cursor-not-allowed"
                    : "bg-ui-bg-base text-ui-fg-base hover:bg-ui-bg-subtle"
                }`}
              >
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 16 16"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    d="M6 4L10 8L6 12"
                    stroke="currentColor"
                    strokeWidth="1.5"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              </Button>
            </div>

            {/* Show per page dropdown on the right */}
            <div className="flex items-center gap-2">
              <Text className="text-sm text-ui-fg-base">Show per Page:</Text>
              <Select
                value={pagination.pageSize.toString()}
                onValueChange={(value) => changePageSize(parseInt(value))}
              >
                <Select.Trigger className="w-[60px] h-8 border-ui-border-base bg-ui-bg-base text-ui-fg-base hover:bg-ui-bg-subtle">
                  <Select.Value />
                </Select.Trigger>
                <Select.Content className="bg-ui-bg-base border-ui-border-base">
                  <Select.Item
                    value="5"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    5
                  </Select.Item>
                  <Select.Item
                    value="10"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    10
                  </Select.Item>
                  <Select.Item
                    value="25"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    25
                  </Select.Item>
                  <Select.Item
                    value="50"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    50
                  </Select.Item>
                  <Select.Item
                    value="100"
                    className="text-ui-fg-base hover:bg-ui-bg-subtle"
                  >
                    100
                  </Select.Item>
                </Select.Content>
              </Select>
            </div>
          </div>
        )}
      </Container>
    </>
  );
};

export const config = defineRouteConfig({
  label: "Destinations",
  icon: DestinationIcon,
});

export default DestinationsPage;
